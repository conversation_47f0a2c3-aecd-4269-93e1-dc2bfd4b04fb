import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import { FileTextOutlined, AlertOutlined, CheckCircleOutlined } from '@ant-design/icons';

const Dashboard: React.FC = () => {
  // 这里使用模拟数据，后续会替换为真实API数据
  const mockData = {
    totalLogs: 1234,
    errorCount: 56,
    successRate: 95.4,
  };

  return (
    <div>
      <h2>系统概览</h2>
      <Row gutter={16}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总日志数"
              value={mockData.totalLogs}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="错误数"
              value={mockData.errorCount}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="成功率"
              value={mockData.successRate}
              prefix={<CheckCircleOutlined />}
              suffix="%"
              precision={2}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard; 