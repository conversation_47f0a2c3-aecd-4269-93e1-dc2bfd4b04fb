body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #202123;
  background-color: #f7f7f8;
  line-height: 1.6;
}

#root {
  min-height: 100vh;
}

* {
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 自定义antd样式 */
.ant-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f2;
  padding: 12px 16px;
  min-height: 48px;
  
  .ant-card-head-title {
    font-size: 16px;
    font-weight: 600;
    color: #202123;
  }
}

.ant-card-body {
  padding: 16px;
}

/* 上传组件样式优化 */
.ant-upload-drag {
  border: 2px dashed rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  
  &:hover {
    border-color: #1890ff !important;
    background: rgba(24, 144, 255, 0.02) !important;
  }
  
  .ant-upload-drag-icon {
    color: #1890ff;
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .ant-upload-text {
    font-size: 15px;
    color: #202123;
    margin: 6px 0;
  }
  
  .ant-upload-hint {
    color: #666;
    font-size: 13px;
    line-height: 1.6;
  }
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    
    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
    }
  }
}

/* 输入框样式优化 */
.ant-input, .ant-input-textarea {
  border-radius: 6px;
  border-color: #e5e7eb;
  transition: all 0.3s ease;
  
  &:hover, &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

/* 列表样式优化 */
.ant-list-item {
  padding: 10px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(24, 144, 255, 0.05);
  }
} 