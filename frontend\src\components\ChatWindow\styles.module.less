.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.messageList {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  margin: 0;
  background: white;
}

.messageItem {
  padding: 8px 0;
  border: none !important;

  &.user {
    flex-direction: row-reverse;
    
    :global {
      .ant-list-item-meta {
        flex-direction: row-reverse;
        margin-inline-start: 0;
        margin-inline-end: 16px;
      }
    }
    
    .messageContent {
      align-items: flex-end;
      
      .text {
        background: #e6f4ff;
        color: #1890ff;
      }
    }
  }
  
  &.system {
    .messageContent {
      align-items: flex-start;
    }
  }

  :global {
    .ant-list-item-meta {
      align-items: flex-start;
      margin-bottom: 0;
    }
  }
}

.messageContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .text {
    background: #f5f5f5;
    padding: 8px 12px;
    border-radius: 12px;
    max-width: 80%;
    word-break: break-word;
    font-size: 14px;
    line-height: 1.6;
  }
  
  .time {
    font-size: 12px;
    color: #999;
    padding: 0 12px;
  }
}

.inputArea {
  display: flex;
  gap: 12px;
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #f0f0f2;
  
  :global {
    .ant-input-textarea {
      flex: 1;
      
      textarea {
        resize: none;
        border-radius: 8px;
        padding: 8px 12px;
        min-height: 40px;
        font-size: 14px;
        line-height: 1.6;
        
        &:hover, &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }
    }
    
    .ant-btn {
      align-self: flex-end;
      height: 40px;
      padding: 0 20px;
      font-size: 14px;
      border-radius: 8px;
    }
  }
} 