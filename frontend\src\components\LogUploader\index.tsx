import React, { useState } from 'react';
import { Upload, Card, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadFile } from '@/types';
import { checkFileSize, checkFileType, formatFileSize, SUPPORTED_LOG_TYPES } from '@/utils/file';
import { uploadService } from '@/services/api';
import { mockUploadResponse } from '@/mock/upload';
import styles from './styles.module.less';

const { Dragger } = Upload;

const LogUploader: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // 处理文件上传
  const handleUpload = async (file: File) => {
    // 检查文件类型
    if (!checkFileType(file.name, 'log')) {
      message.error(`不支持的文件类型，请上传 ${SUPPORTED_LOG_TYPES.join('/')} 格式的文件`);
      return false;
    }

    // 检查文件大小
    if (!checkFileSize(file.size)) {
      message.error(`文件大小不能超过 ${formatFileSize(50 * 1024 * 1024)}`);
      return false;
    }

    try {
      setUploading(true);
      // 这里使用mock数据，实际开发时替换为真实API
      const response = await mockUploadResponse(file);
      if (response.code === 200) {
        message.success('文件上传成功');
        // 读取文件内容进行预览
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setFileList([{
            id: response.data.fileId,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'done',
            url: response.data.url,
            content,
          }]);
        };
        reader.readAsText(file);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('文件上传失败');
    } finally {
      setUploading(false);
    }
    return false;
  };

  return (
    <Card title="日志文件上传" className="log-uploader">
      <Dragger
        className={styles.uploader}
        accept={SUPPORTED_LOG_TYPES.join(',')}
        beforeUpload={handleUpload}
        fileList={fileList}
        disabled={uploading}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持的文件类型：{SUPPORTED_LOG_TYPES.join('/')}
          <br />
          单个文件大小限制：{formatFileSize(50 * 1024 * 1024)}
        </p>
      </Dragger>
      {fileList.length > 0 && fileList[0].content && (
        <div className={styles.preview}>
          <h3>文件预览</h3>
          <pre>{fileList[0].content}</pre>
        </div>
      )}
    </Card>
  );
};

export default LogUploader; 