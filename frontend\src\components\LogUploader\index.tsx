import React, { useState } from 'react';
import { Upload, Card, message, But<PERSON>, Slider } from 'antd';
import { InboxOutlined, DeleteOutlined, ExpandOutlined, CompressOutlined } from '@ant-design/icons';
import type { UploadFile } from '@/types';
import { checkFileSize, checkFileType, formatFileSize, SUPPORTED_LOG_TYPES } from '@/utils/file';
import { uploadService } from '@/services/api';
import { mockUploadResponse } from '@/mock/upload';
import styles from './styles.module.less';

const { Dragger } = Upload;

const LogUploader: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [previewHeight, setPreviewHeight] = useState<number>(200); // 默认预览区域高度
  const [isExpanded, setIsExpanded] = useState<boolean>(false); // 是否处于展开状态

  // 处理文件上传
  const handleUpload = async (file: File) => {
    // 检查文件类型
    if (!checkFileType(file.name, 'log')) {
      message.error(`不支持的文件类型，请上传 ${SUPPORTED_LOG_TYPES.join('/')} 格式的文件`);
      return false;
    }

    // 检查文件大小
    if (!checkFileSize(file.size)) {
      message.error(`文件大小不能超过 ${formatFileSize(50 * 1024 * 1024)}`);
      return false;
    }

    try {
      setUploading(true);
      // 这里使用mock数据，实际开发时替换为真实API
      const response = await mockUploadResponse(file);
      if (response.code === 200) {
        message.success('文件上传成功');
        // 读取文件内容进行预览
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setFileList([{
            uid: Date.now().toString(),
            id: response.data.fileId,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'done',
            url: response.data.url,
            content,
          }]);
        };
        reader.readAsText(file);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('文件上传失败');
    } finally {
      setUploading(false);
    }
    return false;
  };

  // 处理文件删除
  const handleFileDelete = () => {
    try {
      // 实际项目中可以调用后端API删除文件
      // 这里只做前端状态处理
      setFileList([]);
      message.success('文件已删除');
    } catch (error) {
      message.error('删除文件失败');
    }
  };

  // 处理预览区域高度变化
  const handleHeightChange = (value: number) => {
    setPreviewHeight(value);
  };

  // 切换预览区域的展开/收起状态
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    setPreviewHeight(isExpanded ? 200 : 500); // 切换为默认高度或较大高度
  };

  return (
    <Card title="日志文件上传" className="log-uploader">
      <Dragger
        className={styles.uploader}
        accept={SUPPORTED_LOG_TYPES.join(',')}
        beforeUpload={handleUpload}
        fileList={fileList}
        disabled={uploading}
        onRemove={handleFileDelete}
        showUploadList={{
          showRemoveIcon: true,
          removeIcon: <DeleteOutlined />
        }}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持的文件类型：{SUPPORTED_LOG_TYPES.join('/')}
          <br />
          单个文件大小限制：{formatFileSize(50 * 1024 * 1024)}
        </p>
      </Dragger>
      {fileList.length > 0 && fileList[0].content && (
        <div className={styles.preview}>
          <div className={styles.previewHeader}>
            <h3>文件预览</h3>
            <div className={styles.previewControls}>
              <Button 
                type="text" 
                icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />} 
                onClick={toggleExpand}
                style={{ marginRight: '10px' }}
              >
                {isExpanded ? '收起' : '展开'}
              </Button>
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                onClick={handleFileDelete}
              >
                删除
              </Button>
            </div>
          </div>
          <div className={styles.previewSlider}>
            <span>预览高度调整：</span>
            <Slider
              min={100}
              max={600}
              value={previewHeight}
              onChange={handleHeightChange}
              style={{ width: '200px', marginLeft: '10px' }}
            />
          </div>
          <pre 
            className={styles.previewContent}
            style={{ maxHeight: `${previewHeight}px`, overflowY: 'auto' }}
          >
            {fileList[0].content}
          </pre>
        </div>
      )}
    </Card>
  );
};

export default LogUploader; 