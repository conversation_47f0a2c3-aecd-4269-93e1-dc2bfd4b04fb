import React from 'react';
import { Table, Card, Input, Space, Tag } from 'antd';
import type { TableProps } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

interface LogEntry {
  key: string;
  timestamp: string;
  level: string;
  message: string;
  source: string;
}

const { Search } = Input;

const LogViewer: React.FC = () => {
  // 模拟数据，后续替换为真实API数据
  const mockData: LogEntry[] = [
    {
      key: '1',
      timestamp: '2024-02-18 10:00:00',
      level: 'INFO',
      message: '系统启动成功',
      source: 'system',
    },
    {
      key: '2',
      timestamp: '2024-02-18 10:01:23',
      level: 'ERROR',
      message: '数据库连接失败',
      source: 'database',
    },
  ];

  const columns: TableProps<LogEntry>['columns'] = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: string) => {
        const color = level === 'ERROR' ? 'red' : level === 'WARN' ? 'orange' : 'green';
        return <Tag color={color}>{level}</Tag>;
      },
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Search
          placeholder="搜索日志内容"
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          onSearch={(value) => console.log(value)}
        />
        <Table
          columns={columns}
          dataSource={mockData}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800, y: 400 }}
        />
      </Space>
    </Card>
  );
};

export default LogViewer; 