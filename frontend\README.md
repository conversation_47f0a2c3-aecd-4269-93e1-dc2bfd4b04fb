# 日志分析系统前端

## 项目简介
日志分析系统的前端部分，基于React + TypeScript + Ant Design开发。提供日志查看、分析和可视化功能。

## 技术栈
- React 18
- TypeScript 5.x
- Ant Design 5.x
- Vite
- React Router DOM
- Axios
- Ant Design Charts

## 开发环境要求
- Node.js 16+
- pnpm 8.x

## 快速开始

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 代码检查
```bash
pnpm lint
```

## 项目结构
```
frontend/
├── doc/                    # 项目文档
│   └── development_log.md  # 开发日志
├── src/                    # 源代码目录
│   ├── components/         # 通用组件
│   │   ├── Layout/        # 布局组件
│   │   ├── LogTable/      # 日志表格组件
│   │   ├── LogFilter/     # 日志筛选组件
│   │   └── Charts/        # 图表组件
│   ├── pages/             # 页面组件
│   │   ├── Dashboard/     # 仪表盘页面
│   │   ├── LogViewer/     # 日志查看页面
│   │   └── Analysis/      # 分析页面
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── mock/              # Mock数据
│   └── assets/            # 静态资源
│       ├── images/        # 图片资源
│       └── styles/        # 样式文件
└── public/                # 公共资源目录

## 开发规范
1. 组件开发
   - 使用TypeScript编写组件
   - 每个组件都需要有PropTypes定义
   - 组件需要写好注释和文档

2. 样式规范
   - 使用CSS-in-JS或Less
   - 遵循BEM命名规范
   - 注意响应式设计

3. 代码提交
   - 提交前运行lint检查
   - 遵循约定式提交规范
   - 保持提交信息清晰明了

## 文档
- [开发日志](./doc/development_log.md)
- [组件文档](./src/components/README.md)
- [API文档](./src/services/README.md)

## 贡献指南
1. Fork本仓库
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建Pull Request

## 版本历史
- v1.0.0 (2024-02-19)
  - 初始化项目
  - 创建基础框架
  - 实现核心页面

# React + TypeScript + Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from 'eslint-plugin-react'

export default tseslint.config({
  // Set the react version
  settings: { react: { version: '18.3' } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs['jsx-runtime'].rules,
  },
})
```
