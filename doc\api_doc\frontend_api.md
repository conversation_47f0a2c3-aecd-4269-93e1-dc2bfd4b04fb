# 前端API文档

## 1. 基础类型定义

### 1.1 通用响应类型
```typescript
// 基础响应类型
interface BaseResponse<T> {
  code: number;        // 状态码
  data: T;            // 响应数据
  message: string;    // 响应消息
}

// 分页请求参数
interface PaginationParams {
  pageSize: number;   // 每页数量
  pageNum: number;    // 页码
}

// 分页响应数据
interface PaginationResponse<T> {
  list: T[];          // 数据列表
  total: number;      // 总数
  hasMore: boolean;   // 是否有更多
}
```

### 1.2 业务类型定义
```typescript
// 基础分析结果
interface AnalysisResult {
  id: string;         // 唯一标识
  type: 'log' | 'code'; // 分析类型
  content: string;    // 分析内容
  timestamp: number;  // 时间戳
}

// 日志分析结果
interface LogAnalysisResult extends Omit<AnalysisResult, 'timestamp'> {
  logLevel: 'info' | 'warning' | 'error'; // 日志级别
  timestamp: string;                      // 格式化的时间戳
  source: string;                         // 日志来源
}

// 代码分析结果
interface CodeAnalysisResult extends AnalysisResult {
  language: string;    // 编程语言
  suggestions: string[]; // 优化建议
  complexity: number;   // 代码复杂度
}

// 消息类型
interface Message {
  id: string;          // 消息ID
  content: string;     // 消息内容
  type: 'user' | 'system'; // 消息类型
  timestamp: number;   // 时间戳
  metadata?: {         // 元数据
    fileId?: string;   // 关联文件ID
    status: string;    // 消息状态
    language?: string; // 编程语言（代码分析时）
    logLevel?: 'info' | 'warning' | 'error'; // 日志级别（日志分析时）
  }
}
```

## 2. API接口规范

### 2.1 文件上传接口

#### 2.1.1 日志文件上传
```typescript
// 请求接口
POST /api/v1/upload/log

// 请求格式
Content-Type: multipart/form-data

// 请求参数
{
  file: File,            // 日志文件
  timestamp: number,     // 上传时间戳
  metadata?: {          // 元数据（可选）
    source: string,     // 日志来源
    type: string,       // 日志类型
    description: string // 描述信息
  }
}

// 响应格式
interface UploadResponse {
  fileId: string;     // 文件唯一标识
  url: string;        // 文件访问URL
  name: string;       // 文件名
  uploadTime: string; // 上传时间
  parseStatus: string;// 解析状态
}

// 响应示例
{
  code: 200,
  data: {
    fileId: "abc123",
    url: "http://example.com/files/abc123",
    name: "system.log",
    uploadTime: "2024-02-20T10:00:00Z",
    parseStatus: "pending"
  },
  message: "上传成功"
}

// 使用示例
const handleUpload = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('timestamp', Date.now().toString());
    
    const response = await fetch('/api/v1/upload/log', {
      method: 'POST',
      body: formData
    });
    const result = await response.json();
    
    if (result.code === 200) {
      // 处理成功响应
    }
  } catch (error) {
    // 处理错误
  }
};
```

#### 2.1.2 代码文件上传
```typescript
// 请求接口
POST /api/v1/upload/code

// 请求参数与响应格式同日志上传
// 额外的metadata字段：
{
  metadata?: {
    language: string,   // 编程语言
    version: string,    // 代码版本
    description: string // 描述信息
  }
}
```

### 2.2 分析接口

#### 2.2.1 获取分析结果
```typescript
// 请求接口
GET /api/v1/analysis/{fileId}

// 响应格式
type AnalysisResponse = BaseResponse<LogAnalysisResult | CodeAnalysisResult>;

// 响应示例（日志分析）
{
  code: 200,
  data: {
    id: "1",
    type: "log",
    content: "发现3个警告，1个错误",
    logLevel: "warning",
    timestamp: "2024-02-20T10:00:00Z",
    source: "system.log"
  },
  message: "分析完成"
}

// 响应示例（代码分析）
{
  code: 200,
  data: {
    id: "1",
    type: "code",
    content: "代码质量分析完成",
    timestamp: 1708416000000,
    language: "python",
    suggestions: [
      "建议优化循环结构",
      "考虑添加错误处理"
    ],
    complexity: 8
  },
  message: "分析完成"
}
```

### 2.3 聊天接口

#### 2.3.1 发送消息
```typescript
// 请求接口
POST /api/v1/chat/message

// 请求参数
interface SendMessageRequest {
  content: string;      // 消息内容
  timestamp: number;    // 发送时间戳
  type: 'user';        // 消息类型
  metadata?: {         // 元数据
    fileId?: string;   // 关联的文件ID
    context?: string[]; // 上下文消息ID列表
  }
}

// 响应格式
interface SendMessageResponse {
  messageId: string;    // 消息ID
  content: string;      // 响应内容
  timestamp: number;    // 响应时间戳
  type: 'system';      // 消息类型
  status: string;      // 消息状态
}

// 使用示例
const sendMessage = async (content: string) => {
  try {
    const response = await fetch('/api/v1/chat/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content,
        timestamp: Date.now(),
        type: 'user'
      })
    });
    const result = await response.json();
    
    if (result.code === 200) {
      // 处理成功响应
    }
  } catch (error) {
    // 处理错误
  }
};
```

#### 2.3.2 获取历史消息
```typescript
// 请求接口
GET /api/v1/chat/history

// 请求参数
interface GetHistoryParams extends PaginationParams {
  fileId?: string;    // 关联的文件ID（可选）
}

// 响应格式
type HistoryResponse = BaseResponse<PaginationResponse<Message>>;

// 响应示例
{
  code: 200,
  data: {
    list: [
      {
        id: "msg1",
        content: "分析结果显示...",
        type: "system",
        timestamp: 1708416000000,
        metadata: {
          fileId: "file1",
          status: "success"
        }
      }
    ],
    total: 100,
    hasMore: true
  },
  message: "获取成功"
}
```

## 3. 错误处理

### 3.1 错误码定义
```typescript
const ErrorCodes = {
  SUCCESS: 200,           // 成功
  PARAM_ERROR: 400,       // 参数错误
  UNAUTHORIZED: 401,      // 未授权
  FORBIDDEN: 403,         // 禁止访问
  NOT_FOUND: 404,        // 资源不存在
  FILE_TOO_LARGE: 413,    // 文件过大
  UNSUPPORTED_TYPE: 415,  // 不支持的文件类型
  SERVER_ERROR: 500,      // 服务器错误
  SERVICE_BUSY: 503       // 服务繁忙
}
```

### 3.2 错误处理示例
```typescript
const handleError = (error: any) => {
  switch (error.code) {
    case ErrorCodes.FILE_TOO_LARGE:
      message.error('文件大小超过限制');
      break;
    case ErrorCodes.UNSUPPORTED_TYPE:
      message.error('不支持的文件类型');
      break;
    default:
      message.error('操作失败，请重试');
  }
};
```

## 4. 开发环境配置

### 4.1 环境变量配置
```typescript
// .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_USE_MOCK=true

// .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_USE_MOCK=false
```

### 4.2 API配置
```typescript
// config/index.ts
export const config = {
  development: {
    baseURL: import.meta.env.VITE_API_BASE_URL,
    useMock: import.meta.env.VITE_USE_MOCK === 'true',
    timeout: 30000
  },
  production: {
    baseURL: import.meta.env.VITE_API_BASE_URL,
    useMock: false,
    timeout: 30000
  }
}[import.meta.env.MODE];
```

## 5. Mock数据配置

### 5.1 Mock开关控制
```typescript
// services/api.ts
const shouldUseMock = () => {
  return config.useMock;
};

export const uploadFile = async (file: File) => {
  if (shouldUseMock()) {
    return mockUploadResponse(file);
  }
  // 实际API调用
};
```

### 5.2 Mock数据示例
```typescript
// mock/data.ts
export const mockSystemResponses = [
  '正在分析日志文件...',
  '发现潜在的问题，请查看分析结果',
  '分析完成，未发现严重问题',
  '建议优化代码结构，详细建议已生成',
  '正在处理您的请求，请稍候...'
];

export const mockLogAnalysis: LogAnalysisResult = {
  id: '1',
  type: 'log',
  content: '发现3个警告，1个错误',
  logLevel: 'warning',
  timestamp: new Date().toISOString(),
  source: 'system.log'
};
``` 