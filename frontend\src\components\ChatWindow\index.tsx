import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, List, Avatar } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import type { ChatMessage } from '@/types';
import styles from './styles.module.less';

const ChatWindow: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSend = async () => {
    if (!inputValue.trim() || sending) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      type: 'user',
      timestamp: Date.now(),
      status: 'sending',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setSending(true);

    // 模拟系统响应
    setTimeout(() => {
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '我已经收到您的消息，正在处理中...',
        type: 'system',
        timestamp: Date.now(),
        status: 'sent',
      };
      setMessages(prev => [...prev, systemMessage]);
      setSending(false);
    }, 1000);
  };

  return (
    <Card title="聊天窗口" className="chat-window">
      <div className={styles.container}>
        <List
          className={styles.messageList}
          dataSource={messages}
          renderItem={(message) => (
            <List.Item className={`${styles.messageItem} ${styles[message.type]}`}>
              <List.Item.Meta
                avatar={
                  <Avatar icon={message.type === 'user' ? <UserOutlined /> : <RobotOutlined />} />
                }
                description={
                  <div className={styles.messageContent}>
                    <div className={styles.text}>{message.content}</div>
                    <div className={styles.time}>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
        <div ref={messagesEndRef} />
        <div className={styles.inputArea}>
          <Input.TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onPressEnter={(e) => {
              if (!e.shiftKey) {
                e.preventDefault();
                handleSend();
              }
            }}
            placeholder="输入消息，按Enter发送，Shift+Enter换行"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            loading={sending}
          >
            发送
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ChatWindow; 