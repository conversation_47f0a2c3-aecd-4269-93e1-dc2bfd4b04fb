// 文件大小限制（50MB）
export const MAX_FILE_SIZE = 50 * 1024 * 1024;

// 支持的文件类型
export const SUPPORTED_LOG_TYPES = ['.log', '.txt'];
export const SUPPORTED_CODE_TYPES = ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.go', '.rs'];

/**
 * 检查文件大小是否超出限制
 * @param size 文件大小（字节）
 */
export const checkFileSize = (size: number): boolean => {
  return size <= MAX_FILE_SIZE;
};

/**
 * 检查文件类型是否支持
 * @param filename 文件名
 * @param type 文件类型（'log' | 'code'）
 */
export const checkFileType = (filename: string, type: 'log' | 'code'): boolean => {
  const ext = '.' + filename.split('.').pop()?.toLowerCase();
  return type === 'log' 
    ? SUPPORTED_LOG_TYPES.includes(ext)
    : SUPPORTED_CODE_TYPES.includes(ext);
};

/**
 * 格式化文件大小
 * @param bytes 文件大小（字节）
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}; 