import type { LogAnalysisResult, CodeAnalysisResult } from './types';

// Mock日志分析结果
export const mockLogAnalysis: LogAnalysisResult = {
  id: '1',
  type: 'log',
  content: '发现3个警告，1个错误',
  logLevel: 'warning',
  timestamp: new Date().toISOString(),
  source: 'system.log',
};

// Mock代码分析结果
export const mockCodeAnalysis: CodeAnalysisResult = {
  id: '1',
  type: 'code',
  content: '代码质量分析完成',
  timestamp: Date.now(),
  language: 'python',
  suggestions: [
    '建议优化循环结构',
    '考虑添加错误处理',
    '可以提取公共函数'
  ],
  complexity: 8,
};

// Mock系统响应消息
export const mockSystemResponses = [
  '正在分析日志文件...',
  '发现潜在的问题，请查看分析结果',
  '分析完成，未发现严重问题',
  '建议优化代码结构，详细建议已生成',
  '正在处理您的请求，请稍候...',
]; 