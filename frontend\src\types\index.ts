// 文件上传相关类型
export interface UploadFile {
  uid: string;
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'done' | 'error';
  progress?: number;
  url?: string;
  content?: string;
}

// 聊天消息类型
export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'system';
  timestamp: number;
  status: 'sending' | 'sent' | 'error';
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// 文件上传响应
export interface UploadResponse {
  fileId: string;
  url: string;
  name: string;
}

// 分析结果类型
export interface AnalysisResult {
  id: string;
  type: 'log' | 'code';
  content: string;
  timestamp: number;
} 