import React from 'react';
import MainLayout from '@/components/Layout';
import LogUploader from '@/components/LogUploader';
import CodeUploader from '@/components/CodeUploader';
import ChatWindow from '@/components/ChatWindow';
import styles from './styles.module.less';

const Home: React.FC = () => {
  return (
    <MainLayout>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* 左侧：上传区域 */}
          <div className={styles.leftSection}>
            <div className={styles.uploadArea}>
              <LogUploader />
              <CodeUploader />
            </div>
          </div>
          {/* 右侧：聊天窗口 */}
          <div className={styles.rightSection}>
            <ChatWindow />
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Home; 