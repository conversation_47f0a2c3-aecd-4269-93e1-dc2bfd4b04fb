{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,css,less}\""}, "dependencies": {"@ant-design/icons": "^5.6.1", "antd": "^5.24.1", "axios": "^1.7.9", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "react-syntax-highlighter": "^15.5.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.2.0", "prettier": "^3.2.5", "typescript": "^5.3.3", "vite": "^5.1.3"}}