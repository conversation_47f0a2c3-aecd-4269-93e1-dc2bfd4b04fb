# 开发注意事项

## 1. 系统环境相关
### Windows 系统特殊注意事项
- 在Windows系统中，删除文件/文件夹需使用PowerShell命令：
  ```powershell
  Remove-Item -Path <路径> -Recurse -Force
  ```
  而不是Linux的 `rm -rf` 命令
  - `-Path`: 指定要删除的文件/文件夹路径
  - `-Recurse`: 递归删除所有子文件夹和文件
  - `-Force`: 强制删除，不询问确认

### 命令行使用注意
- Windows PowerShell 和 Linux bash 命令有差异，需要注意区分
- 在Windows中使用PowerShell时，许多Linux命令需要替换为对应的PowerShell命令
- 示例对照表：
  | Linux命令 | Windows PowerShell命令 |
  |-----------|----------------------|
  | rm -rf    | Remove-Item -Recurse -Force |
  | ls        | Get-ChildItem (或 dir) |
  | cp        | Copy-Item |
  | mv        | Move-Item |

## 2. 项目开发注意事项
### 前端开发
- 使用pnpm作为包管理工具
- 技术栈：React 18 + Ant Design 5.x + Vite + TypeScript
- 开发前确保已安装所需环境：
  - Node.js
  - pnpm
  - TypeScript

#### 布局开发经验总结
1. 页面布局问题与解决方案
   - 问题描述：页面出现大量留白，内容无法填满整个视口
   - 原因分析：
     * 布局结构嵌套过深
     * 容器高度计算不准确
     * 内边距和外边距设置不合理
   - 解决方案：
     * 使用 flex + grid 混合布局
     * 精确计算容器高度
     * 移除冗余的内边距和外边距

2. 关键修改点
   - Layout组件：
     ```less
     .layout {
       min-height: 100vh;
       display: flex;
       flex-direction: column;
     }
     .content {
       flex: 1;
       height: calc(100vh - 56px);
       overflow: hidden;
     }
     ```
   - Home页面布局：
     ```less
     .container {
       min-height: 100vh;
       display: flex;
       flex-direction: column;
     }
     .content {
       flex: 1;
       display: grid;
       grid-template-columns: 360px 1fr;
       grid-template-rows: 1fr;
       height: calc(100vh - 56px);
     }
     ```

3. 布局最佳实践
   - 容器高度设置：
     * 使用 `100vh` 确保填满视口
     * 使用 `calc()` 精确计算剩余空间
     * 避免使用固定像素值
   - 弹性布局：
     * 顶层容器使用 flex 布局
     * 子元素使用 grid 布局进行分栏
     * 避免过多的嵌套层级
   - 间距处理：
     * 统一使用 padding 控制内间距
     * 使用 gap 控制网格间距
     * 避免使用 margin 造成塌陷

4. 常见陷阱
   - 避免使用百分比高度，可能导致计算错误
   - 注意清除默认的内边距和外边距
   - 谨慎使用绝对定位，可能破坏布局流
   - 避免使用 `overflow: scroll`，优先使用 `auto`

5. 调试技巧
   - 使用浏览器开发工具检查元素尺寸
   - 添加临时边框查看布局结构
   - 使用 CSS Grid 开发工具辅助调试
   - 在不同屏幕尺寸下测试布局

6. 复杂布局重构案例分析
   - 问题背景：
     * 页面存在多个上传区域和聊天窗口
     * 布局结构不合理，导致高度不一致
     * 存在多余的留白和间隙
   
   - 问题诊断过程：
     * 检查开发者工具中的DOM结构
     * 发现布局被错误地分成了四个部分
     * 样式和实际渲染的类名不完全匹配
     * 组件嵌套结构不合理

   - 解决方案：
     1. 组件结构优化
        ```tsx
        // 优化前：三个独立区域
        <div className={styles.leftSection}>
          <LogUploader />
        </div>
        <div className={styles.rightSection}>
          <ChatWindow />
        </div>
        <div className={styles.middleSection}>
          <CodeUploader />
        </div>

        // 优化后：两栏布局
        <div className={styles.leftSection}>
          <div className={styles.uploadArea}>
            <LogUploader />
            <CodeUploader />
          </div>
        </div>
        <div className={styles.rightSection}>
          <ChatWindow />
        </div>
        ```

     2. 样式优化要点
        ```less
        // 容器布局
        .content {
          display: grid;
          grid-template-columns: 360px 1fr;  // 固定左侧宽度
          height: calc(100vh - 56px);        // 精确计算高度
        }

        // 上传区域
        .uploadArea {
          display: flex;
          flex-direction: column;
          height: 100%;
          
          .log-uploader, .code-uploader {
            flex: 1;  // 均分高度
            margin: 0;  // 移除间距
            border-bottom: 1px solid #f0f0f2;  // 添加分隔线
          }
        }

        // 聊天窗口
        .chat-window {
          flex: 1;
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        ```

   - 关键经验：
     1. 布局结构简化
        * 避免过多的布局嵌套
        * 使用语义化的容器划分
        * 保持组件结构清晰

     2. 高度控制
        * 使用flex布局自动分配空间
        * 为关键容器设置明确的高度
        * 移除不必要的固定高度计算

     3. 间距处理
        * 统一使用padding控制内间距
        * 使用border替代margin作为分隔
        * 移除冗余的margin和padding

     4. 样式优化
        * 移除多余的样式属性
        * 统一边框和间距样式
        * 使用flex和grid混合布局

   - 调试技巧：
     * 使用浏览器开发者工具检查DOM结构
     * 通过类名定位样式问题
     * 逐层检查容器的尺寸和间距

   - 最佳实践：
     1. 布局规划
        * 先确定整体布局结构
        * 再细化具体区域样式
        * 最后处理细节和交互

     2. 样式管理
        * 使用BEM命名规范
        * 避免样式嵌套过深
        * 及时清理无用样式

     3. 响应式处理
        * 使用flex布局自适应
        * 避免固定像素值
        * 考虑不同屏幕尺寸

7. 布局调试和迭代优化经验
   - 问题表现形式：
     * 看似简单的布局修改可能需要多次调整
     * 修复一个问题可能引发其他布局问题
     * 开发者工具显示的结构与预期不符
   
   - 迭代优化策略：
     1. 系统性诊断
        * 不要急于修改代码，先理解整体布局结构
        * 使用开发者工具分析实际渲染结果
        * 找出样式和DOM结构的差异点
        * 类似医生看病，先诊断后开药

     2. 渐进式修改
        * 每次只修改一个关键点
        * 修改后立即验证效果
        * 保留可回退的修改记录
        * 就像搭积木，一块一块稳固地搭建

     3. 组件结构重构
        * 优先考虑组件的语义化组织
        * 减少不必要的嵌套层级
        * 确保组件职责单一清晰
        * 如同整理房间，先规划空间布局

     4. 样式调优过程
        * 从外到内逐层优化
        * 移除所有可疑的样式属性
        * 重新构建必要的样式规则
        * 像修剪盆景，先去除杂枝

   - 常见陷阱与解决方案：
     1. 样式覆盖问题
        * 检查样式优先级
        * 注意全局样式影响
        * 使用更具体的选择器
        * 避免使用!important

     2. 布局抖动
        * 设置明确的容器尺寸
        * 避免内容导致的尺寸变化
        * 使用占位符维持结构
        * 控制滚动条的显示影响

     3. 响应式适配
        * 测试不同视口大小
        * 验证布局断点设置
        * 确保弹性布局正常工作
        * 检查内容溢出情况

   - 开发流程优化建议：
     1. 代码审查
        * 每次修改后进行自我代码审查
        * 检查样式命名的语义性
        * 验证布局的可维护性
        * 确保代码注释完整

     2. 文档记录
        * 记录关键的修改决策
        * 保存典型的问题解决方案
        * 更新组件使用说明
        * 维护样式指南文档

     3. 团队协作
        * 分享布局优化经验
        * 建立通用的布局模式
        * 统一代码风格规范
        * 促进知识经验沉淀

   - 经验总结：
     * 布局优化是一个渐进的过程，需要耐心和系统性思维
     * 良好的组件结构是布局稳定的基础
     * 样式管理需要遵循一致的规范
     * 持续的文档记录有助于经验积累和团队成长
     * 就像建筑工程，需要规划、施工、验收的完整流程

### 后端开发
- Python版本要求：3.8+
- 使用poetry进行依赖管理
- 运行环境配置需要：
  - pyenv（Python版本管理）
  - poetry（依赖管理）
  - MySQL 8.0
  - Redis（可选，用于缓存）

## 3. 文档维护建议
- 重要的系统配置变更需要及时记录
- 遇到的环境问题和解决方案要及时补充
- 定期更新依赖版本说明
- 保持文档的结构清晰，便于团队其他成员查阅

## 4. 版本控制
- 遵循语义化版本控制规范
- 重要更改需要在CHANGELOG.md中记录
- 分支管理规范：
  - main: 主分支，保持稳定
  - develop: 开发分支
  - feature/*: 功能分支
  - hotfix/*: 紧急修复分支

## 5. 定期更新
本文档将持续更新，记录开发过程中遇到的重要注意事项和最佳实践。最后更新时间：2024-02-20 