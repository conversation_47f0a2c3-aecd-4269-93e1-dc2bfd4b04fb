import { createBrowserRouter } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import LogViewer from './pages/LogViewer';
import Analysis from './pages/Analysis';
import Layout from './components/Layout';

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: '/',
        element: <Dashboard />,
      },
      {
        path: '/logs',
        element: <LogViewer />,
      },
      {
        path: '/analysis',
        element: <Analysis />,
      },
    ],
  },
]);

export default router; 