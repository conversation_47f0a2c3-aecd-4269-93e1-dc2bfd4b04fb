.uploader {
  margin-bottom: 24px;
  
  :global {
    .ant-upload-drag {
      height: 200px;
    }
  }
}

.preview {
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;

  h3 {
    margin-bottom: 0;
    color: #1890ff;
  }

  pre {
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.previewControls {
  display: flex;
  align-items: center;
}

.previewSlider {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  span {
    color: #666;
    font-size: 14px;
  }
}

.previewContent {
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
} 