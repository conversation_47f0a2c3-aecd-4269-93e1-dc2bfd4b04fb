log_analysis/
│
├── __init__.py                # 初始化模块
├── main.py                    # 主程序入口
├── doc/                       # 文档目录
│   ├── log_analysis_readme.md # 项目主要说明文档
│   ├── development_notes.md   # 开发注意事项文档
│   ├── api_doc/              # API文档目录
│   │   ├── backend_api.md    # 后端API文档
│   │   └── frontend_api.md   # 前端API文档
│   └── design/               # 设计文档目录
│       ├── database.md       # 数据库设计文档
│       └── architecture.md   # 系统架构设计文档
│
├── config/                    # 配置模块
│   ├── __init__.py            # 初始化模块
│   └── config.py              # 存储项目的配置参数
│
├── backend/                   # 后端模块
│   ├── __init__.py            # 初始化模块
│   ├── app.py                 # 后端应用程序入口
│   ├── log_processor/         # 日志处理模块
│   │   ├── __init__.py        # 初始化模块
│   │   ├── parser/            # 日志解析器模块
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   └── parser.py      # 负责解析日志文件
│   │   ├── analyzer/          # 日志分析器模块
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   └── analyzer.py    # 负责分析解析后的数据
│   │   └── visualizer/        # 数据可视化模块
│   │       ├── __init__.py    # 初始化模块
│   │       └── visualizer.py  # 负责生成图表和报告
│   │
│   ├── user_queries/          # 用户提问处理模块
│   │   ├── __init__.py        # 初始化模块
│   │   ├── handler/           # 用户提问处理逻辑模块
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   └── query_handler.py # 处理用户提问的逻辑
│   │   └── response/          # 用户响应生成模块
│   │       ├── __init__.py    # 初始化模块
│   │       └── response_generator.py # 生成用户提问的响应
│   │
│   ├── business_plugins/      # 业务逻辑插件模块
│   │   ├── __init__.py        # 初始化模块
│   │   ├── base/              # 基础插件模块
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   ├── plugin_base.py # 插件基类定义
│   │   │   └── registry.py    # 插件注册器
│   │   ├── vpc/               # VPC业务插件
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   ├── vpc_plugin.py  # VPC插件实现
│   │   │   ├── chain.py       # VPC思维链定义
│   │   │   └── models.py      # VPC数据模型
│   │   ├── floating_ip/         # 公网IP业务插件
│   │   │   ├── __init__.py    # 初始化模块
│   │   │   ├── ip_plugin.py   # 公网IP插件实现
│   │   │   ├── chain.py       # 公网IP思维链定义
│   │   │   └── models.py      # 公网IP数据模型
│   │   └── security_group/    # 安全组业务插件
│   │       ├── __init__.py    # 初始化模块
│   │       ├── sg_plugin.py   # 安全组插件实现
│   │       ├── chain.py       # 安全组思维链定义
│   │       └── models.py      # 安全组数据模型
│   │
│   └── llm_interface/         # LLM 接口分析模块
│       ├── __init__.py        # 初始化模块
│       ├── llm_client.py       # 负责与 LLM 接口的交互
│       └── llm_analyzer.py     # 负责分析 LLM 返回的数据
│
├── frontend/                  # 前端项目根目录
│   ├── doc/                    # 前端文档目录
│   │   └── development_log.md  # 开发日志
│   ├── public/                # 静态公共资源
│   │   ├── index.html         # HTML模板
│   │   └── favicon.ico        # 网站图标
│   │
│   ├── src/                   # 源代码目录
│   │   ├── components/        # 通用组件
│   │   │   ├── Layout/       # 布局组件
│   │   │   │   └── index.tsx # 布局组件实现
│   │   │   ├── FileUploader/ # 文件上传组件
│   │   │   │   └── index.tsx # 上传组件实现
│   │   │   ├── FilePreview/  # 文件预览组件
│   │   │   │   └── index.tsx # 预览组件实现
│   │   │   └── ChatBox/      # 聊天组件
│   │   │       └── index.tsx # 聊天组件实现
│   │   │
│   │   ├── pages/            # 页面组件
│   │   │   ├── LogUploader/  # 日志上传页面
│   │   │   │   ├── index.tsx # 页面实现
│   │   │   │   └── style.ts  # 页面样式
│   │   │   ├── CodeUploader/ # 代码上传页面
│   │   │   │   ├── index.tsx # 页面实现
│   │   │   │   └── style.ts  # 页面样式
│   │   │   └── ChatWindow/   # 聊天窗口页面
│   │   │       ├── index.tsx # 页面实现
│   │   │       └── style.ts  # 页面样式
│   │   │
│   │   ├── services/         # API服务
│   │   │   ├── api.ts        # API接口定义
│   │   │   ├── request.ts    # 请求封装
│   │   │   └── types.ts      # 接口类型定义
│   │   │
│   │   ├── utils/            # 工具函数
│   │   │   ├── file.ts       # 文件处理工具
│   │   │   ├── storage.ts    # 本地存储工具
│   │   │   └── validator.ts  # 验证工具
│   │   │
│   │   ├── mock/             # Mock数据
│   │   │   ├── upload.ts     # 上传响应数据
│   │   │   └── chat.ts       # 聊天记录数据
│   │   │
│   │   ├── assets/           # 静态资源
│   │   │   ├── images/       # 图片资源
│   │   │   └── styles/       # 样式文件
│   │   │       ├── global.less   # 全局样式
│   │   │       └── variables.less # 样式变量
│   │   │
│   │   ├── routes.tsx        # 路由配置
│   │   ├── main.tsx         # 应用入口
│   │   └── vite-env.d.ts    # 类型声明
│   │
│   ├── package.json          # 项目配置
│   ├── tsconfig.json        # TypeScript配置
│   └── vite.config.ts      # Vite配置
│   └── eslint.config.js    # ESLint配置
│
└── README.md                  # 项目说明文档

# 技术方案说明

## 1. 技术栈选择

### 1.1 后端技术栈
- Web框架：FastAPI（异步支持，自动API文档）
- 数据库：MySQL 8.0
- ORM框架：SQLAlchemy 2.0
- 数据库连接：PyMySQL
- 数据处理：Pandas（日志解析）+ NumPy（数据分析）
- 数据库迁移：Alembic

### 1.2 前端技术栈
- 基础框架: React 18
- UI组件库: Ant Design 5.x
- 状态管理: React Context + Hooks
- 请求库: Axios
- 构建工具: Vite
- 包管理: pnpm

### 1.3 部署方案
- 容器化部署：Docker + Docker Compose
- 本地部署：直接Python环境运行
- 配置管理：JSON配置文件

## 2. 数据库设计

### 2.1 主要数据表
```sql
-- 日志记录表
CREATE TABLE log_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME,
    log_level VARCHAR(10),
    message VARCHAR(500),
    source VARCHAR(100),
    raw_data JSON
);

-- 分析结果表
CREATE TABLE analysis_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50),
    result_data JSON,
    created_at DATETIME
);

-- 业务插件配置表
CREATE TABLE plugin_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plugin_name VARCHAR(50),
    config_data JSON,
    enabled BOOLEAN DEFAULT true,
    created_at DATETIME,
    updated_at DATETIME
);

-- 业务分析结果表
CREATE TABLE business_analysis_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    plugin_name VARCHAR(50),
    analysis_type VARCHAR(50),
    result_data JSON,
    created_at DATETIME,
    source_query_id INT,
    FOREIGN KEY (source_query_id) REFERENCES user_queries(id)
);
```

## 3. 模块说明

### 3.1 日志处理模块 (log_processor)
- parser: 负责解析不同格式的日志文件，支持CSV、JSON、文本等格式
- analyzer: 实现日志分析算法，包括异常检测、趋势分析等
- visualizer: 生成可视化图表，支持多种展示方式

### 3.2 用户查询模块 (user_queries)
- handler: 处理用户输入的自然语言查询
- response: 生成结构化的查询响应

### 3.3 LLM接口模块 (llm_interface)
- llm_client: 与大模型API交互
- llm_analyzer: 分析大模型返回结果

### 3.4 业务逻辑插件模块 (business_plugins)
- base: 提供插件系统的基础框架
  - plugin_base: 定义插件接口和通用功能
  - registry: 管理插件的注册和发现
- vpc: VPC网络相关业务逻辑
  - vpc_plugin: 实现VPC相关的分析和处理逻辑
  - chain: 定义VPC业务的思维链和推理过程
  - models: VPC相关的数据模型定义
- public_ip: 公网IP相关业务逻辑
  - ip_plugin: 实现公网IP相关的分析和处理逻辑
  - chain: 定义公网IP业务的思维链和推理过程
  - models: 公网IP相关的数据模型定义
- security_group: 安全组相关业务逻辑
  - sg_plugin: 实现安全组相关的分析和处理逻辑
  - chain: 定义安全组业务的思维链和推理过程
  - models: 安全组相关的数据模型定义

## 4. 部署说明

### 4.1 Docker部署
```yaml
# docker-compose.yml 配置示例
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=mysql+pymysql://user:password@db/log_analysis
    volumes:
      - ../config:/app/config

  db:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=log_analysis
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
      - MYSQL_ROOT_PASSWORD=root_password
    volumes:
      - mysql_data:/var/lib/mysql
```

### 4.2 本地部署
1. 环境要求：
   - pyenv（Python版本管理）
   - poetry（依赖管理）
   - MySQL 8.0
   - Redis（可选，用于缓存）

2. Python环境配置：
   ```bash
   # 使用pyenv安装并设置Python版本
   pyenv install 3.8.10
   pyenv local 3.8.10

   # 安装poetry
   curl -sSL https://install.python-poetry.org | python3 -

   # 验证安装
   poetry --version
   ```

3. 项目依赖管理：
   ```bash
   # 初始化poetry项目（如果是新项目）
   poetry init

   # 安装项目依赖
   poetry install

   # 添加新依赖
   poetry add fastapi sqlalchemy pymysql pandas numpy
   poetry add --group dev pytest pytest-cov
   ```

4. 项目配置文件 `pyproject.toml`：
   ```toml
   [tool.poetry]
   name = "log_analysis"
   version = "0.1.0"
   description = "日志分析系统"
   authors = ["Your Name <<EMAIL>>"]

   [tool.poetry.dependencies]
   python = "^3.8"
   fastapi = "^0.104.0"
   sqlalchemy = "^2.0.0"
   pymysql = "^1.1.0"
   pandas = "^2.1.0"
   numpy = "^1.24.0"
   python-dotenv = "^1.0.0"
   pydantic = "^2.4.0"
   pydantic-settings = "^2.0.0"
   uvicorn = "^0.23.0"
   jinja2 = "^3.1.2"
   alembic = "^1.12.0"

   [tool.poetry.group.dev.dependencies]
   pytest = "^7.4.0"
   pytest-cov = "^4.1.0"
   black = "^23.9.0"
   isort = "^5.12.0"
   flake8 = "^6.1.0"

   [build-system]
   requires = ["poetry-core>=1.0.0"]
   build-backend = "poetry.core.masonry.api"

   [tool.black]
   line-length = 88
   target-version = ['py38']

   [tool.isort]
   profile = "black"
   multi_line_output = 3
   ```

5. 启动应用：
   ```bash
   # 激活虚拟环境
   poetry shell

   # 初始化数据库
   python -m backend.database

   # 启动应用
   python main.py
   ```

6. 开发工作流：
   ```bash
   # 进入项目虚拟环境
   poetry shell

   # 运行测试
   pytest

   # 代码格式化
   black .
   isort .

   # 代码质量检查
   flake8
   ```

7. 依赖管理：
   ```bash
   # 更新所有依赖
   poetry update

   # 导出依赖到requirements.txt（用于Docker构建）
   poetry export -f requirements.txt --output requirements.txt
   ```

### 4.3 Docker部署（使用Poetry）
```dockerfile
# Dockerfile
FROM python:3.8-slim

# 安装poetry
RUN pip install poetry

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY pyproject.toml poetry.lock ./
COPY . .

# 配置poetry不创建虚拟环境（在Docker中不需要）
RUN poetry config virtualenvs.create false

# 安装依赖
RUN poetry install --no-dev

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
```

## 5. 配置文件说明
```json
{
    "server": {
        "host": "0.0.0.0",
        "port": 8000,
        "debug": false
    },
    "database": {
        "type": "mysql",
        "host": "localhost",
        "port": 3306,
        "username": "log_analysis",
        "password": "your_password",
        "database": "log_analysis_db"
    },
    "log_parser": {
        "log_path": "/path/to/logs",
        "log_pattern": "*.log",
        "batch_size": 1000
    },
    "cache": {
        "enabled": true,
        "redis_url": "redis://localhost:6379/0"
    }
}
```

## 6. 开发计划

### 第一阶段：前端框架搭建
1. 前端项目初始化
2. 基础组件开发
3. 页面路由配置
4. Mock数据准备

### 第二阶段：前端功能实现
1. 日志查看页面开发
2. 分析展示页面开发
3. 数据可视化实现
4. 用户交互优化

### 第三阶段：后端基础架构
1. 项目结构搭建
2. 数据库设计与实现
3. 配置系统实现
4. API接口设计

### 第四阶段：后端功能实现
1. 日志解析器开发
2. 分析器实现
3. API接口开发
4. 数据处理优化

### 第五阶段：系统联调与部署
1. 前后端接口联调
2. Docker环境配置
3. 本地部署脚本
4. 性能优化

## 7. 注意事项
1. 代码提交前需要运行测试用例
2. 配置文件中的敏感信息需要加密处理
3. 日志文件路径需要根据实际环境配置
4. 数据库连接信息请勿硬编码在代码中