import type { ApiResponse, UploadResponse, AnalysisResult } from '@/types';
import { mockLogAnalysis, mockCodeAnalysis, mockSystemResponses } from './data';

// 模拟文件上传响应
export const mockUploadResponse = (file: File): Promise<ApiResponse<UploadResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          fileId: Math.random().toString(36).substring(7),
          url: URL.createObjectURL(file),
          name: file.name,
        },
        message: '上传成功',
      });
    }, 1000);
  });
};

// 模拟分析响应
export const mockAnalysisResponse = (fileId: string, type: 'log' | 'code'): Promise<ApiResponse<AnalysisResult>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const response = type === 'log' 
        ? { ...mockLogAnalysis, timestamp: new Date(mockLogAnalysis.timestamp).getTime() }
        : mockCodeAnalysis;
        
      resolve({
        code: 200,
        data: response,
        message: '分析完成',
      });
    }, 2000);
  });
};

// 模拟系统消息响应
export const mockSystemResponse = (): Promise<string> => {
  return new Promise((resolve) => {
    const randomIndex = Math.floor(Math.random() * mockSystemResponses.length);
    setTimeout(() => {
      resolve(mockSystemResponses[randomIndex]);
    }, 1000);
  });
}; 