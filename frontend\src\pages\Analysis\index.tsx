import React from 'react';
import { Card, Row, Col, Select } from 'antd';
import { Line } from '@ant-design/charts';

const Analysis: React.FC = () => {
  // 模拟数据，后续替换为真实API数据
  const mockData = {
    logTrend: [
      { date: '2024-02-12', count: 120 },
      { date: '2024-02-13', count: 150 },
      { date: '2024-02-14', count: 180 },
      { date: '2024-02-15', count: 140 },
      { date: '2024-02-16', count: 160 },
      { date: '2024-02-17', count: 190 },
      { date: '2024-02-18', count: 170 },
    ],
  };

  const config = {
    data: mockData.logTrend,
    xField: 'date',
    yField: 'count',
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="分析维度">
            <Select
              style={{ width: 200 }}
              placeholder="选择分析维度"
              options={[
                { value: 'time', label: '时间维度' },
                { value: 'source', label: '来源维度' },
                { value: 'level', label: '日志级别' },
              ]}
            />
          </Card>
        </Col>
        <Col span={24}>
          <Card title="日志趋势分析">
            <Line {...config} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Analysis; 