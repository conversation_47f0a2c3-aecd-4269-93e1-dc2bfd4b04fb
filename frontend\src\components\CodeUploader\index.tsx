import React, { useState } from 'react';
import { Upload, Card, message, List } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter';
import { github } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import type { UploadFile } from '@/types';
import { checkFileSize, checkFileType, formatFileSize, SUPPORTED_CODE_TYPES } from '@/utils/file';
import { mockUploadResponse } from '@/mock/upload';
import styles from './styles.module.less';

const { Dragger } = Upload;

const CodeUploader: React.FC = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<UploadFile | null>(null);

  // 处理文件上传
  const handleUpload = async (file: File) => {
    // 检查文件类型
    if (!checkFileType(file.name, 'code')) {
      message.error(`不支持的文件类型，请上传 ${SUPPORTED_CODE_TYPES.join('/')} 格式的文件`);
      return false;
    }

    // 检查文件大小
    if (!checkFileSize(file.size)) {
      message.error(`文件大小不能超过 ${formatFileSize(50 * 1024 * 1024)}`);
      return false;
    }

    try {
      setUploading(true);
      // 这里使用mock数据，实际开发时替换为真实API
      const response = await mockUploadResponse(file);
      if (response.code === 200) {
        message.success('文件上传成功');
        // 读取文件内容进行预览
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          const newFile = {
            uid: response.data.fileId,
            id: response.data.fileId,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'done',
            url: response.data.url,
            content,
          } as UploadFile;
          
          setFileList(prev => [...prev, newFile]);
          setSelectedFile(newFile);
        };
        reader.readAsText(file);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('文件上传失败');
    } finally {
      setUploading(false);
    }
    return false;
  };

  // 获取文件语言类型
  const getLanguage = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase() || '';
    const languageMap: { [key: string]: string } = {
      js: 'javascript',
      ts: 'typescript',
      py: 'python',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      go: 'go',
      rs: 'rust',
    };
    return languageMap[ext] || 'text';
  };

  return (
    <Card title="代码文件上传" className="code-uploader">
      <div className={styles.container}>
        <div className={styles.uploadList}>
          <Dragger
            className={styles.uploader}
            accept={SUPPORTED_CODE_TYPES.join(',')}
            beforeUpload={handleUpload}
            fileList={fileList}
            disabled={uploading}
            multiple
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持的文件类型：{SUPPORTED_CODE_TYPES.join('/')}
              <br />
              单个文件大小限制：{formatFileSize(50 * 1024 * 1024)}
            </p>
          </Dragger>
          {fileList.length > 0 && (
            <List
              className={styles.fileList}
              size="small"
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item
                  className={file.id === selectedFile?.id ? styles.selected : ''}
                  onClick={() => setSelectedFile(file)}
                >
                  {file.name}
                </List.Item>
              )}
            />
          )}
        </div>
        {selectedFile?.content && (
          <div className={styles.preview}>
            <h3>文件预览: {selectedFile.name}</h3>
            <SyntaxHighlighter
              language={getLanguage(selectedFile.name)}
              style={github}
              customStyle={{
                margin: 0,
                padding: '16px',
                borderRadius: '4px',
                maxHeight: '600px',
              }}
            >
              {selectedFile.content}
            </SyntaxHighlighter>
          </div>
        )}
      </div>
    </Card>
  );
};

export default CodeUploader; 