.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 56px;
  background: white;
  padding: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  
  h1 {
    height: 56px;
    margin: 0;
    padding: 0 24px;
    color: #202123;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 24px;
      height: 24px;
      margin-right: 12px;
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border-radius: 6px;
    }
  }
}

.content {
  flex: 1;
  height: calc(100vh - 56px);
  overflow: hidden;
} 