.container {
  display: flex;
  gap: 24px;
  height: 100%;
}

.uploadList {
  width: 300px;
  display: flex;
  flex-direction: column;
}

.uploader {
  margin-bottom: 16px;
  
  :global {
    .ant-upload-drag {
      height: 200px;
    }
  }
}

.fileList {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  
  :global {
    .ant-list-item {
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background: #f5f5f5;
      }
    }
  }
}

.selected {
  background: #e6f7ff !important;
}

.preview {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  h3 {
    margin-bottom: 16px;
    color: #1890ff;
  }
} 