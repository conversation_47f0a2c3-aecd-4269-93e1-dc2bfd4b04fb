import axios from 'axios';
import type { UploadFile, ApiResponse, UploadResponse, AnalysisResult } from '@/types';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 文件上传服务
export const uploadService = {
  // 上传日志文件
  uploadLog: async (file: File): Promise<ApiResponse<UploadResponse>> => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post<ApiResponse<UploadResponse>>('/upload/log', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 上传代码文件
  uploadCode: async (file: File): Promise<ApiResponse<UploadResponse>> => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post<ApiResponse<UploadResponse>>('/upload/code', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// 分析服务
export const analysisService = {
  // 分析日志文件
  analyzeLog: async (fileId: string): Promise<ApiResponse<AnalysisResult>> => {
    const response = await api.post<ApiResponse<AnalysisResult>>('/analyze/log', { fileId });
    return response.data;
  },

  // 分析代码文件
  analyzeCode: async (fileId: string): Promise<ApiResponse<AnalysisResult>> => {
    const response = await api.post<ApiResponse<AnalysisResult>>('/analyze/code', { fileId });
    return response.data;
  },
}; 